import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ViewService, ViewDescriptor, ViewContainerDescriptor } from '../../../src/main/services/view.service';
import type { LoggingServiceAPI } from '../../../src/main/services/logging.service';
import type { IpcServiceAPI } from '../../../src/main/services/ipc.service';

describe('ViewService', () => {
  let viewService: ViewService;
  let mockLogger: jest.Mocked<LoggingServiceAPI>;
  let mockIpcService: jest.Mocked<IpcServiceAPI>;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<LoggingServiceAPI>;

    // Create mock IPC service
    mockIpcService = {
      handle: jest.fn(),
      send: jest.fn(),
      removeHandler: jest.fn(),
      notifyShutdown: jest.fn(),
      invoke: jest.fn(),
      sendToRenderer: jest.fn(),
      on: jest.fn(),
      removeAllListeners: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<IpcServiceAPI>;

    viewService = new ViewService(mockLogger, mockIpcService);
  });

  describe('Initialization', () => {
    it('should initialize view service successfully', async () => {
      const mockWindowGetter = jest.fn(() => null);

      await viewService.initialize(mockWindowGetter);

      expect(mockIpcService.handle).toHaveBeenCalledWith(
        'core:views.getRegistered',
        expect.any(Function)
      );
      expect(mockIpcService.handle).toHaveBeenCalledWith(
        'core:views.getRegisteredContainers',
        expect.any(Function)
      );
    });

    it('should register default views during initialization', async () => {
      const mockWindowGetter = jest.fn(() => null);

      await viewService.initialize(mockWindowGetter);

      const views = viewService.getRegisteredViews();
      const containers = viewService.getRegisteredViewContainers();

      // Should have some default views and containers
      expect(views.length).toBeGreaterThan(0);
      expect(containers.length).toBeGreaterThan(0);

      // Check for specific default views
      const viewIds = views.map(v => v.id);
      expect(viewIds).toContain('workbench.views.notifications');

      // Check for specific default containers
      const containerIds = containers.map(c => c.id);
      expect(containerIds).toContain('workbench');
    });
  });

  describe('View Registration', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await viewService.initialize(mockWindowGetter);
    });

    it('should register a view successfully', () => {
      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: 'TestView',
        extensionId: 'test.extension',
        location: 'sidebar',
        icon: 'test-icon',
        when: 'always',
      };

      const disposable = viewService.registerView(view);

      expect(disposable).toBeDefined();
      expect(typeof disposable.dispose).toBe('function');
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining("Регистрация представления: 'test.view'")
      );

      const registeredViews = viewService.getRegisteredViews();
      expect(registeredViews).toContainEqual(view);
    });

    it('should warn when registering duplicate view', () => {
      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: 'TestView',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      viewService.registerView(view);
      viewService.registerView(view); // Register again

      expect(mockLogger.warn).toHaveBeenCalledWith(
        "Представление с ID 'test.view' уже зарегистрировано. Перезаписываем."
      );
    });

    it('should handle view registration without ID', () => {
      const view = {
        name: 'Test View',
        componentName: 'TestView',
        extensionId: 'test.extension',
        location: 'sidebar',
      } as ViewDescriptor;

      const disposable = viewService.registerView(view);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Попытка зарегистрировать представление без ID.'
      );
      expect(typeof disposable.dispose).toBe('function');
    });

    it('should handle view registration without extensionId', () => {
      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: 'TestView',
        extensionId: '',
        location: 'sidebar',
      };

      expect(() => viewService.registerView(view)).toThrow(
        "Представление 'test.view' должно иметь extensionId."
      );
    });

    it('should handle view registration without componentName', () => {
      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: '',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      expect(() => viewService.registerView(view)).toThrow(
        "Представление 'test.view' должно иметь componentName."
      );
    });

    it('should dispose view registration', async () => {
      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: 'TestView',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      const disposable = viewService.registerView(view);
      await disposable.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith(
        "Отмена регистрации представления: 'test.view'"
      );

      const registeredViews = viewService.getRegisteredViews();
      expect(registeredViews).not.toContainEqual(view);
    });
  });

  describe('View Container Registration', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await viewService.initialize(mockWindowGetter);
    });

    it('should register a view container successfully', () => {
      const container: ViewContainerDescriptor = {
        id: 'test.container',
        title: 'Test Container',
        icon: 'test-icon',
        viewId: 'test.view',
      };

      const disposable = viewService.registerViewContainer(container);

      expect(disposable).toBeDefined();
      expect(typeof disposable.dispose).toBe('function');
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining("Регистрация контейнера представлений: 'test.container'")
      );

      const registeredContainers = viewService.getRegisteredViewContainers();
      expect(registeredContainers).toContainEqual(container);
    });

    it('should warn when registering duplicate container', () => {
      const container: ViewContainerDescriptor = {
        id: 'test.container',
        title: 'Test Container',
        icon: 'test-icon',
        viewId: 'test.view',
      };

      viewService.registerViewContainer(container);
      viewService.registerViewContainer(container); // Register again

      expect(mockLogger.warn).toHaveBeenCalledWith(
        "Контейнер представлений с ID 'test.container' уже зарегистрирован. Перезаписываем."
      );
    });

    it('should handle container registration without ID', () => {
      const container = {
        title: 'Test Container',
        icon: 'test-icon',
        viewId: 'test.view',
      } as ViewContainerDescriptor;

      const disposable = viewService.registerViewContainer(container);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Попытка зарегистрировать контейнер представлений без ID.'
      );
      expect(typeof disposable.dispose).toBe('function');
    });

    it('should dispose container registration', async () => {
      const container: ViewContainerDescriptor = {
        id: 'test.container',
        title: 'Test Container',
        icon: 'test-icon',
        viewId: 'test.view',
      };

      const disposable = viewService.registerViewContainer(container);
      await disposable.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith(
        "Отмена регистрации контейнера представлений: 'test.container'"
      );

      const registeredContainers = viewService.getRegisteredViewContainers();
      expect(registeredContainers).not.toContainEqual(container);
    });
  });

  describe('View Retrieval', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await viewService.initialize(mockWindowGetter);
    });

    it('should return all registered views', () => {
      const view1: ViewDescriptor = {
        id: 'test.view1',
        name: 'Test View 1',
        componentName: 'TestView1',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      const view2: ViewDescriptor = {
        id: 'test.view2',
        name: 'Test View 2',
        componentName: 'TestView2',
        extensionId: 'test.extension',
        location: 'panel',
      };

      viewService.registerView(view1);
      viewService.registerView(view2);

      const views = viewService.getRegisteredViews();
      expect(views).toContainEqual(view1);
      expect(views).toContainEqual(view2);
    });

    it('should return all registered view containers', () => {
      const container1: ViewContainerDescriptor = {
        id: 'test.container1',
        title: 'Test Container 1',
        icon: 'test-icon1',
        viewId: 'test.view1',
      };

      const container2: ViewContainerDescriptor = {
        id: 'test.container2',
        title: 'Test Container 2',
        icon: 'test-icon2',
        viewId: 'test.view2',
      };

      viewService.registerViewContainer(container1);
      viewService.registerViewContainer(container2);

      const containers = viewService.getRegisteredViewContainers();
      expect(containers).toContainEqual(container1);
      expect(containers).toContainEqual(container2);
    });
  });

  describe('IPC Integration', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await viewService.initialize(mockWindowGetter);
    });

    it('should register IPC handlers during initialization', () => {
      expect(mockIpcService.handle).toHaveBeenCalledWith(
        'core:views.getRegistered',
        expect.any(Function)
      );
      expect(mockIpcService.handle).toHaveBeenCalledWith(
        'core:views.getRegisteredContainers',
        expect.any(Function)
      );
    });

    it('should handle IPC request for registered views', async () => {
      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: 'TestView',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      viewService.registerView(view);

      // Get the handler function that was registered
      const handleCall = mockIpcService.handle.mock.calls.find(
        call => call[0] === 'core:views.getRegistered'
      );
      expect(handleCall).toBeDefined();

      const handler = handleCall![1];
      const result = await handler();

      expect(result).toContainEqual(view);
    });

    it('should handle IPC request for registered containers', async () => {
      const container: ViewContainerDescriptor = {
        id: 'test.container',
        title: 'Test Container',
        icon: 'test-icon',
        viewId: 'test.view',
      };

      viewService.registerViewContainer(container);

      // Get the handler function that was registered
      const handleCall = mockIpcService.handle.mock.calls.find(
        call => call[0] === 'core:views.getRegisteredContainers'
      );
      expect(handleCall).toBeDefined();

      const handler = handleCall![1];
      const result = await handler();

      expect(result).toContainEqual(container);
    });
  });

  describe('View Location Types', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await viewService.initialize(mockWindowGetter);
    });

    it('should register views with different locations', () => {
      const sidebarView: ViewDescriptor = {
        id: 'test.sidebar',
        name: 'Sidebar View',
        componentName: 'SidebarView',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      const panelView: ViewDescriptor = {
        id: 'test.panel',
        name: 'Panel View',
        componentName: 'PanelView',
        extensionId: 'test.extension',
        location: 'panel',
      };

      const statusbarView: ViewDescriptor = {
        id: 'test.statusbar',
        name: 'Statusbar View',
        componentName: 'StatusbarView',
        extensionId: 'test.extension',
        location: 'statusbar',
        alignment: 'left',
        priority: 1,
      };

      viewService.registerView(sidebarView);
      viewService.registerView(panelView);
      viewService.registerView(statusbarView);

      const views = viewService.getRegisteredViews();
      expect(views).toContainEqual(sidebarView);
      expect(views).toContainEqual(panelView);
      expect(views).toContainEqual(statusbarView);
    });
  });

  describe('Disposal', () => {
    it('should dispose all registered views and containers', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await viewService.initialize(mockWindowGetter);

      const view: ViewDescriptor = {
        id: 'test.view',
        name: 'Test View',
        componentName: 'TestView',
        extensionId: 'test.extension',
        location: 'sidebar',
      };

      const container: ViewContainerDescriptor = {
        id: 'test.container',
        title: 'Test Container',
        icon: 'test-icon',
        viewId: 'test.view',
      };

      viewService.registerView(view);
      viewService.registerViewContainer(container);

      await viewService.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith(
        "Отмена регистрации представления: 'test.view'"
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        "Отмена регистрации контейнера представлений: 'test.container'"
      );
    });
  });
});
