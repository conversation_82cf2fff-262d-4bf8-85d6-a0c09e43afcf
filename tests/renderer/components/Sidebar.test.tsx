import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { Sidebar } from '../../../src/renderer/components/Sidebar/Sidebar';
import type { ViewDescriptor } from '../../../src/main/services/view.service';

// Mock the view registry
jest.mock('../../../src/renderer/core/viewRegistry', () => ({
  getViewImporter: jest.fn(),
}));

// Mock view components
const MockView1 = ({ viewId }: { viewId: string }) => (
  <div data-testid={`view-${viewId}`}>Mock View 1 Content</div>
);

const MockView2 = ({ viewId }: { viewId: string }) => (
  <div data-testid={`view-${viewId}`}>Mock View 2 Content</div>
);

const MockErrorView = () => {
  throw new Error('Mock view error');
};

describe('Sidebar', () => {
  const mockViews: ViewDescriptor[] = [
    {
      id: 'view1',
      name: 'View 1',
      componentName: 'MockView1',
      containerId: 'container1',
    },
    {
      id: 'view2',
      name: 'View 2',
      componentName: 'MockView2',
      containerId: 'container1',
    },
    {
      id: 'view3',
      name: 'View 3',
      componentName: 'MockView3',
      containerId: 'container1',
    },
  ];

  let mockGetViewImporter: jest.MockedFunction<any>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get the mocked function
    const { getViewImporter } = require('../../../src/renderer/core/viewRegistry');
    mockGetViewImporter = getViewImporter as jest.MockedFunction<any>;

    // Setup default mock implementations
    mockGetViewImporter.mockImplementation((componentName: string) => {
      switch (componentName) {
        case 'MockView1':
          return Promise.resolve({ default: MockView1 });
        case 'MockView2':
          return Promise.resolve({ default: MockView2 });
        case 'MockView3':
          return Promise.resolve({ default: MockView3 });
        case 'MockErrorView':
          return Promise.resolve({ default: MockErrorView });
        default:
          return Promise.reject(new Error(`View component not found: ${componentName}`));
      }
    });
  });

  describe('rendering', () => {
    it('should render sidebar with views', async () => {
      render(
        <Sidebar
          sidebarViews={mockViews}
          activeViewId="view1"
        />
      );

      expect(screen.getByText('Loading View 1...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByTestId('view-view1')).toBeInTheDocument();
      });
    });

    it('should render "No views available" when no views provided', () => {
      render(
        <Sidebar
          sidebarViews={[]}
          activeViewId={null}
        />
      );

      expect(screen.getByText('No views available')).toBeInTheDocument();
    });

    it('should render "No views available" when views is null/undefined', () => {
      render(
        <Sidebar
          sidebarViews={null as any}
          activeViewId={null}
        />
      );

      expect(screen.getByText('No views available')).toBeInTheDocument();
    });

    it('should apply custom style when provided', () => {
      const customStyle = { backgroundColor: 'blue', width: '300px' };

      render(
        <Sidebar
          sidebarViews={mockViews}
          activeViewId="view1"
          style={customStyle}
        />
      );

      const sidebar = document.querySelector('.sidebar');
      expect(sidebar).toHaveStyle('background-color: blue');
      expect(sidebar).toHaveStyle('width: 300px');
    });
  });

  describe('view visibility', () => {
    it('should show only the active view', async () => {
      render(
        <Sidebar
          sidebarViews={mockViews}
          activeViewId="view2"
        />
      );

      await waitFor(() => {
        const view1 = screen.getByTestId('view-view1').parentElement;
        const view2 = screen.getByTestId('view-view2').parentElement;

        // Check style attributes instead of computed styles
        expect(view1?.getAttribute('style')).toContain('display: none');
        expect(view2?.getAttribute('style')).toContain('display: block');
      });
    });

    it('should hide all views when activeViewId is null', async () => {
      render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)} // Only use views that don't error
          activeViewId={null}
        />
      );

      await waitFor(() => {
        const view1 = screen.getByTestId('view-view1').parentElement;
        const view2 = screen.getByTestId('view-view2').parentElement;

        // Check style attributes instead of computed styles
        expect(view1?.getAttribute('style')).toContain('display: none');
        expect(view2?.getAttribute('style')).toContain('display: none');
      });
    });

    it('should hide all views when activeViewId does not match any view', async () => {
      render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)}
          activeViewId="non-existent"
        />
      );

      await waitFor(() => {
        const view1 = screen.getByTestId('view-view1').parentElement;
        const view2 = screen.getByTestId('view-view2').parentElement;

        // Check style attributes instead of computed styles
        expect(view1?.getAttribute('style')).toContain('display: none');
        expect(view2?.getAttribute('style')).toContain('display: none');
      });
    });
  });

  describe('view loading', () => {
    it('should show loading state while view is loading', () => {
      render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 1)}
          activeViewId="view1"
        />
      );

      expect(screen.getByText('Loading View 1...')).toBeInTheDocument();
    });

    it('should load view component dynamically', async () => {
      render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 1)}
          activeViewId="view1"
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Mock View 1 Content')).toBeInTheDocument();
      });

      expect(mockGetViewImporter).toHaveBeenCalledWith('MockView1');
    });

    it('should cache loaded view components', async () => {
      const { rerender } = render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 1)}
          activeViewId="view1"
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Mock View 1 Content')).toBeInTheDocument();
      });

      // Re-render with same view
      rerender(
        <Sidebar
          sidebarViews={mockViews.slice(0, 1)}
          activeViewId="view1"
        />
      );

      // Should not call getViewImporter again due to caching
      expect(mockGetViewImporter).toHaveBeenCalledTimes(1);
    });
  });

  describe('error handling', () => {
    it('should handle view loading errors gracefully', async () => {
      // Suppress console.error for this test
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <Sidebar
          sidebarViews={[mockViews[2]]} // view3 which throws error
          activeViewId="view3"
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Error loading view: View 3')).toBeInTheDocument();
      });

      consoleSpy.mockRestore();
    });

    it('should handle missing view importer', () => {
      mockGetViewImporter.mockReturnValue(null);

      render(
        <Sidebar
          sidebarViews={[mockViews[0]]}
          activeViewId="view1"
        />
      );

      expect(screen.getByText('Error loading view: View 1')).toBeInTheDocument();
    });

    it('should handle view without componentName', () => {
      const viewWithoutComponent: ViewDescriptor = {
        id: 'no-component',
        name: 'No Component',
        containerId: 'container1',
      };

      render(
        <Sidebar
          sidebarViews={[viewWithoutComponent]}
          activeViewId="no-component"
        />
      );

      expect(screen.getByText('Error loading view: No Component')).toBeInTheDocument();
    });
  });

  describe('view switching', () => {
    it('should switch between views correctly', async () => {
      const { rerender } = render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)}
          activeViewId="view1"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('view-view1')).toBeInTheDocument();
      });

      // Switch to view2
      rerender(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)}
          activeViewId="view2"
        />
      );

      await waitFor(() => {
        const view1 = screen.getByTestId('view-view1').parentElement;
        const view2 = screen.getByTestId('view-view2').parentElement;

        // Check style attributes instead of computed styles
        expect(view1?.getAttribute('style')).toContain('display: none');
        expect(view2?.getAttribute('style')).toContain('display: block');
      });
    });

    it('should maintain view instances when switching', async () => {
      const { rerender } = render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)}
          activeViewId="view1"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('view-view1')).toBeInTheDocument();
      });

      const view1Element = screen.getByTestId('view-view1');

      // Switch to view2 and back to view1
      rerender(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)}
          activeViewId="view2"
        />
      );

      rerender(
        <Sidebar
          sidebarViews={mockViews.slice(0, 2)}
          activeViewId="view1"
        />
      );

      // View1 should still be the same instance
      expect(screen.getByTestId('view-view1')).toBe(view1Element);
    });
  });

  describe('memoization', () => {
    it('should memoize ViewInstance components', async () => {
      const { rerender } = render(
        <Sidebar
          sidebarViews={mockViews.slice(0, 1)}
          activeViewId="view1"
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('view-view1')).toBeInTheDocument();
      });

      const initialViewElement = screen.getByTestId('view-view1');

      // Re-render with same props
      rerender(
        <Sidebar
          sidebarViews={mockViews.slice(0, 1)}
          activeViewId="view1"
        />
      );

      // Should be the same element due to memoization
      expect(screen.getByTestId('view-view1')).toBe(initialViewElement);
    });
  });
});
