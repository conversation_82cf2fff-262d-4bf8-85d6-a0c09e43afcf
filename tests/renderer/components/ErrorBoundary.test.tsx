import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import { ErrorBoundary, withErrorBoundary, useErrorHandler } from '../../../src/renderer/components/ErrorBoundary';

// Mock the IPC service
jest.mock('../../../src/renderer/core/services/ipcRendererService', () => ({
  ipcRendererService: {
    invoke: jest.fn(),
  },
}));

// Test component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean }> = ({ shouldThrow = false }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

// Test component for useErrorHandler hook
const ErrorHandlerTestComponent: React.FC = () => {
  const handleError = useErrorHandler();
  
  const triggerError = () => {
    handleError(new Error('Hook test error'), { context: 'test' });
  };
  
  return (
    <button onClick={triggerError}>Trigger Error</button>
  );
};

describe('ErrorBoundary', () => {
  let mockInvoke: jest.MockedFunction<any>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Get the mocked function
    const { ipcRendererService } = require('../../../src/renderer/core/services/ipcRendererService');
    mockInvoke = ipcRendererService.invoke as jest.MockedFunction<any>;

    // Suppress console.error for these tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('when no error occurs', () => {
    it('should render children normally', () => {
      render(
        <ErrorBoundary>
          <div>Test content</div>
        </ErrorBoundary>
      );

      expect(screen.getByText('Test content')).toBeInTheDocument();
    });
  });

  describe('when an error occurs', () => {
    it('should catch error and display fallback UI', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Try Again' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Reload Application' })).toBeInTheDocument();
    });

    it('should display component name in error message when provided', () => {
      render(
        <ErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('TestComponent Error')).toBeInTheDocument();
    });

    it('should call onError callback when provided', () => {
      const onError = jest.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String),
        })
      );
    });

    it('should report error to main process via IPC', async () => {
      mockInvoke.mockResolvedValue(undefined);

      render(
        <ErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      // Wait for async error reporting
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockInvoke).toHaveBeenCalledWith(
        'core:error.report',
        expect.objectContaining({
          message: 'Test error',
          source: 'renderer-TestComponent',
          severity: 'high',
          handled: true,
        })
      );
    });

    it('should handle IPC error reporting failure gracefully', async () => {
      mockInvoke.mockRejectedValue(new Error('IPC failed'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      // Wait for async error reporting
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to report error to main process'),
        expect.any(Error)
      );
    });
  });

  describe('error recovery', () => {
    it('should recover when Try Again is clicked', () => {
      const { rerender } = render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();

      fireEvent.click(screen.getByRole('button', { name: 'Try Again' }));

      // Re-render with no error
      rerender(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      );

      expect(screen.getByText('No error')).toBeInTheDocument();
      expect(screen.queryByText('Something went wrong')).not.toBeInTheDocument();
    });

    it('should reload application when Reload Application is clicked', () => {
      const mockReload = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { reload: mockReload },
        writable: true,
      });

      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      );

      fireEvent.click(screen.getByRole('button', { name: 'Reload Application' }));

      expect(mockReload).toHaveBeenCalled();
    });
  });

  describe('withErrorBoundary HOC', () => {
    it('should wrap component with error boundary', () => {
      const TestComponent = () => <div>Wrapped component</div>;
      const WrappedComponent = withErrorBoundary(TestComponent, { componentName: 'HOCTest' });

      render(<WrappedComponent />);

      expect(screen.getByText('Wrapped component')).toBeInTheDocument();
    });

    it('should catch errors in wrapped component', () => {
      const WrappedComponent = withErrorBoundary(ThrowError, { componentName: 'HOCTest' });

      render(<WrappedComponent shouldThrow={true} />);

      expect(screen.getByText('HOCTest Error')).toBeInTheDocument();
    });
  });

  describe('useErrorHandler hook', () => {
    it('should report errors to main process', async () => {
      mockInvoke.mockResolvedValue(undefined);

      render(<ErrorHandlerTestComponent />);

      fireEvent.click(screen.getByRole('button', { name: 'Trigger Error' }));

      // Wait for async error reporting
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(mockInvoke).toHaveBeenCalledWith(
        'core:error.report',
        expect.objectContaining({
          message: 'Hook test error',
          source: 'renderer-hook',
          severity: 'medium',
          handled: true,
          metadata: expect.objectContaining({
            errorInfo: { context: 'test' },
          }),
        })
      );
    });

    it('should handle IPC reporting failure gracefully', async () => {
      mockInvoke.mockRejectedValue(new Error('IPC failed'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(<ErrorHandlerTestComponent />);

      fireEvent.click(screen.getByRole('button', { name: 'Trigger Error' }));

      // Wait for async error reporting
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to report error to main process'),
        expect.any(Error)
      );
    });
  });
});
