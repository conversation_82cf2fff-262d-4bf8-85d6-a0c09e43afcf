import React from 'react';
import { render, screen } from '@testing-library/react';
import { jest } from '@jest/globals';

// Mock the Icon component since we don't have the actual implementation
// This test serves as an example of how to test simple components
const Icon: React.FC<{ name: string; size?: number; className?: string }> = ({ 
  name, 
  size = 16, 
  className 
}) => (
  <span 
    data-testid={`icon-${name}`} 
    data-size={size}
    className={className}
    style={{ fontSize: size }}
  >
    {name}
  </span>
);

describe('Icon Component', () => {
  describe('rendering', () => {
    it('should render icon with name', () => {
      render(<Icon name="Search" />);
      
      expect(screen.getByTestId('icon-Search')).toBeInTheDocument();
      expect(screen.getByText('Search')).toBeInTheDocument();
    });

    it('should render with default size', () => {
      render(<Icon name="Home" />);

      const icon = screen.getByTestId('icon-Home');
      expect(icon).toHaveAttribute('data-size', '16');
      // Check style attribute instead of computed style
      const styleAttr = icon.getAttribute('style') || '';
      expect(styleAttr).toContain('font-size: 16px');
    });

    it('should render with custom size', () => {
      render(<Icon name="Settings" size={24} />);

      const icon = screen.getByTestId('icon-Settings');
      expect(icon).toHaveAttribute('data-size', '24');
      // Check style attribute instead of computed style
      const styleAttr = icon.getAttribute('style') || '';
      expect(styleAttr).toContain('font-size: 24px');
    });

    it('should apply custom className', () => {
      render(<Icon name="User" className="custom-icon" />);
      
      const icon = screen.getByTestId('icon-User');
      expect(icon).toHaveClass('custom-icon');
    });

    it('should handle different icon names', () => {
      const iconNames = ['FolderOpen', 'GitBranch', 'HelpCircle', 'ChevronDown'];
      
      iconNames.forEach(name => {
        render(<Icon name={name} />);
        expect(screen.getByTestId(`icon-${name}`)).toBeInTheDocument();
      });
    });
  });

  describe('accessibility', () => {
    it('should be accessible to screen readers', () => {
      render(<Icon name="Search" />);
      
      const icon = screen.getByTestId('icon-Search');
      expect(icon).toBeInTheDocument();
      // In a real implementation, you might add aria-label or role attributes
    });
  });

  describe('edge cases', () => {
    it('should handle empty name', () => {
      render(<Icon name="" />);
      
      expect(screen.getByTestId('icon-')).toBeInTheDocument();
    });

    it('should handle zero size', () => {
      render(<Icon name="Test" size={0} />);

      const icon = screen.getByTestId('icon-Test');
      expect(icon).toHaveAttribute('data-size', '0');
      // Check style attribute instead of computed style
      const styleAttr = icon.getAttribute('style') || '';
      expect(styleAttr).toContain('font-size: 0px');
    });

    it('should handle negative size', () => {
      render(<Icon name="Test" size={-10} />);
      
      const icon = screen.getByTestId('icon-Test');
      expect(icon).toHaveAttribute('data-size', '-10');
      expect(icon).toHaveStyle('font-size: -10px');
    });

    it('should handle very large size', () => {
      render(<Icon name="Test" size={1000} />);

      const icon = screen.getByTestId('icon-Test');
      expect(icon).toHaveAttribute('data-size', '1000');
      // Check style attribute instead of computed style
      const styleAttr = icon.getAttribute('style') || '';
      expect(styleAttr).toContain('font-size: 1000px');
    });
  });
});
