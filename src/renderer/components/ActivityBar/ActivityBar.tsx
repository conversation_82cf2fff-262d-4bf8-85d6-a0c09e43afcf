import React from "react";
import cn from "classnames";
import type { ViewContainerDescriptor } from "@main/services/view.service";
import { Icon } from "../Icon/Icon";
// Import types and service removed as they're not used
import "./ActivityBar.css";

interface ActivityBarProps {
  containers: ViewContainerDescriptor[];
  activeContainerId: string | null;
  onContainerClick: (id: string) => void;
  style?: React.CSSProperties; // Добавляем проп style
}

export const ActivityBar: React.FC<ActivityBarProps> = React.memo(
  ({
    containers,
    activeContainerId,
    onContainerClick,
    style, // Получаем style из пропсов
  }) => {
    return (
      // Применяем стиль к корневому элементу
      <div className="activity-bar" style={style}>
        {containers.map((container) => (
          <button
            key={container.id}
            title={container.title}
            aria-label={container.title}
            className={cn({ active: container.id === activeContainerId })}
            onClick={() => {
              // Only update the state via the passed callback (which uses Zustand)
              onContainerClick(container.id);
              // Removed the redundant CORE_CONTEXT_SET command via IPC
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onContainerClick(container.id);
              }
            }}
          >
            <Icon name={container.icon || "HelpCircle"} size={24} />
          </button>
        ))}
      </div>
    );
  }
);

ActivityBar.displayName = "ActivityBar";
