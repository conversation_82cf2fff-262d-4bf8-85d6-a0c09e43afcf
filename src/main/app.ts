import {app, Event} from 'electron';
import {logger as rootLogger, LoggingServiceAPI} from '@services/logging.service';
import {ServiceManager} from './service.manager';
import {WindowManager} from './window.manager';
import {ExtensionManager} from './extension.manager';
import {registerIpcHandlers} from './ipc/handlers';
import {CommandIds} from '@shared/constants/command-ids';
import {AppLifecycleEvent} from '@shared/types/lifecycle-events';

export class MainApplication {
  private serviceManager: ServiceManager | null = null;
  private windowManager: WindowManager | null = null;
  private extensionManager: ExtensionManager | null = null;
  private readonly logger: LoggingServiceAPI;
  
  constructor(logger: LoggingServiceAPI) {
    this.logger = logger.createScopedLogger('MainApplication');
    this.registerAppListeners();
  }
  
  /**
   * Initializes the application, services, window, and extensions.
   * Should be called once the app is ready.
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing...');
    
    // Create ServiceManager first to get ConfigurationService
    this.serviceManager = new ServiceManager(rootLogger); // Pass only logger initially
    
    // Create WindowManager, injecting ConfigurationService
    this.windowManager = new WindowManager(rootLogger, this.serviceManager.configuration);
    
    // Update ServiceManager with WindowManager (if needed by other services)
    // This dependency direction might need review later if it causes issues
    this.serviceManager.setWindowManager(this.windowManager);
    
    // Removed updateSettingsService call
    
    // Создаем ExtensionManager
    this.extensionManager = new ExtensionManager(rootLogger, this.serviceManager);
    
    if (!this.serviceManager || !this.windowManager || !this.extensionManager) {
      this.logger.error('Core managers not available during initialization.');
      app.quit();
      return;
    }
    
    // Регистрируем IPC-обработчики с использованием WindowManager
    registerIpcHandlers(
      this.serviceManager, // Передаем весь реестр сервисов
      () => this.windowManager?.getCurrentWindow() || null, // Используем метод WindowManager
    );
    
    // Инициализируем сервисы с доступом к окну через WindowManager
    this.logger.info("Initializing services...");
    await this.serviceManager.initializeServices(() => this.windowManager?.getCurrentWindow() || null);
    
    // Обнаруживаем и активируем расширения
    this.logger.info("Discovering and activating extensions...");
    await this.extensionManager.discoverExtensions();
    await this.extensionManager.activateAll();
    
    // Только после инициализации всех сервисов и расширений создаем и показываем окно
    this.logger.info("Creating main window...");
    const mainWindow = await this.windowManager.createMainWindow();
    if (!mainWindow) {
      this.logger.error("Main window creation failed. Exiting.");
      app.quit();
      return;
    }
    
    // Окно показывается сразу при создании
    this.logger.info("Main window created and shown.");
    
    // TODO: close window only when all disposed
    // mainWindow.on("close", (event) => {
    //   if(!this.canExit) event.preventDefault();
    //
    //   mainWindow.close();
    // });
  }
  
  /**
   * Registers listeners for Electron app events.
   */
  private registerAppListeners(): void {
    // Use initialize method on 'ready'
    app.on('ready', () => this.initialize().catch(err => {
      this.logger.error("Fatal error during MainApplication initialization:", err);
      app.quit();
    }));
    app.on('activate', () => this.onActivate());
    app.on('window-all-closed', () => this.onWindowAllClosed());
    app.on('will-quit', (event) => this.onWillQuit(event));
  }
  
  
  /**
   * Called on macOS when the dock icon is clicked and there are no other windows open.
   */
  private async onActivate() {
    await this.initialize();
  }
  
  /**
   * Called when all windows are closed. Quit the app, except on macOS.
   */
  private onWindowAllClosed(): void {
    if (process.platform !== 'darwin') {
      this.logger.info('All windows closed, quitting app.');
      app.quit();
    }
  }
  
  /**
   * Called before the application starts closing its windows.
   * Handles deactivation of extensions and disposal of services.
   */
  private async onWillQuit(event: Event): Promise<void> {
    this.logger.info('Application is about to quit. Setting shutdown flag, triggering save, deactivating extensions and disposing services...');
    event.preventDefault(); // Prevent immediate exit
    
    try {
      if (!this.serviceManager) {
        this.logger.error('ServiceManager not available during shutdown.');
        return;
      }
      
      // Получаем сервис жизненного цикла для emit событий
      const lifecycle = this.serviceManager.lifecycle;
      
      // Trigger save all signal before disposing anything
      if (this.serviceManager?.commands) {
        this.logger.info(`Triggering ${CommandIds.SAVE_ALL} signal...`);
        
        // Emit event о начале сохранения
        await lifecycle.emit(AppLifecycleEvent.SAVE_ALL_TRIGGERED);
        
        try {
          // Дожидаемся выполнения команды сохранения
          await this.serviceManager.commands.executeCommand(
            {commandId: CommandIds.SAVE_ALL, args: {}}
          );
          await lifecycle.emit(AppLifecycleEvent.SAVE_ALL_COMPLETE);
        } catch (saveErr) {
          this.logger.error(`Error executing ${CommandIds.SAVE_ALL} during shutdown:`, saveErr);
        }
        
        // Небольшая задержка для обработки событий
        await new Promise(resolve => setTimeout(resolve, 150));
      } else {
        this.logger.warn('CommandService not available to trigger saveAll.');
      }
      
      // Notify IpcService that shutdown is starting BEFORE disposing extensions/services
      if (this.serviceManager?.ipc) {
        this.serviceManager.ipc.notifyShutdown();
      }
      
      // Deactivate extensions first
      if (this.extensionManager) {
        this.logger.info('Deactivating extensions...');
        await this.extensionManager.dispose();
        await lifecycle.emit(AppLifecycleEvent.EXTENSIONS_DEACTIVATED);
      }
      
      // Dispose services via the registry
      if (this.serviceManager) {
        this.logger.info('Disposing service manager...');
        await this.serviceManager.dispose();
      }
      
      this.logger.info('Extensions and services handled successfully.');
      
      // Final app quit event
      await lifecycle.emit(AppLifecycleEvent.APP_QUIT);
      
    } catch {
      this.logger.error('Error during shutdown');
    } finally {
      app.exit(); // Exit after cleanup
    }
  }
}
