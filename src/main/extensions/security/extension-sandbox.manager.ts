/**
 * Extension Sandbox Manager
 * Manages sandboxing, isolation, and security enforcement for extensions
 *
 * Note: This file uses 'any' types for sandbox API compatibility.
 */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { LoggingServiceAPI } from '../../services/logging.service';
import { ExtensionSecurityService, ExtensionPermission } from './extension-security.service';
import { ExtensionManifest } from '../extension.types';
import { createServiceError, ErrorSeverity } from '@shared/errors';
import * as fs from 'fs';
import * as http from 'http';
import * as https from 'https';

/**
 * Sandbox configuration for an extension
 */
export interface ExtensionSandboxConfig {
  extensionId: string;
  isolateMemory: boolean;
  isolateFileSystem: boolean;
  isolateNetwork: boolean;
  allowedModules: string[];
  deniedModules: string[];
  maxExecutionTime: number;
  enableResourceMonitoring: boolean;
}

/**
 * Extension execution context
 */
export interface ExtensionExecutionContext {
  extensionId: string;
  startTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  isRunning: boolean;
  violations: number;
}

/**
 * Built-in trusted extensions (core extensions that ship with the application)
 */
const TRUSTED_EXTENSIONS = new Set([
  'book-ide.books',
  'book-ide.characters',
  'book-ide.core',
  'book-ide.themes',
]);

/**
 * Dangerous Node.js modules that should be restricted
 */
const DANGEROUS_MODULES = new Set([
  'child_process',
  'cluster',
  'dgram',
  'dns',
  'fs',
  'http',
  'https',
  'net',
  'os',
  'process',
  'repl',
  'tls',
  'vm',
  'worker_threads',
]);

/**
 * Safe Node.js modules that extensions can use
 */
const SAFE_MODULES = new Set([
  'path',
  'url',
  'querystring',
  'util',
  'events',
  'stream',
  'buffer',
  'string_decoder',
  'crypto', // Limited crypto operations
]);

/**
 * Extension Sandbox Manager
 */
export class ExtensionSandboxManager {
  private readonly logger: LoggingServiceAPI;
  private readonly securityService: ExtensionSecurityService;
  private readonly sandboxConfigs = new Map<string, ExtensionSandboxConfig>();
  private readonly executionContexts = new Map<string, ExtensionExecutionContext>();
  private readonly moduleInterceptor = new Map<string, any>();

  constructor(logger: LoggingServiceAPI, securityService: ExtensionSecurityService) {
    this.logger = logger.createScopedLogger('ExtensionSandboxManager');
    this.securityService = securityService;
    this.setupModuleInterceptor();
  }

  /**
   * Initialize sandbox for an extension
   */
  initializeSandbox(extensionId: string, _manifest: ExtensionManifest): ExtensionSandboxConfig {
    const isTrusted = TRUSTED_EXTENSIONS.has(extensionId);
    
    const config: ExtensionSandboxConfig = {
      extensionId,
      isolateMemory: !isTrusted,
      isolateFileSystem: !isTrusted,
      isolateNetwork: !isTrusted,
      allowedModules: isTrusted ? [...SAFE_MODULES, ...DANGEROUS_MODULES] : [...SAFE_MODULES],
      deniedModules: isTrusted ? [] : [...DANGEROUS_MODULES],
      maxExecutionTime: isTrusted ? 60000 : 30000, // 60s for trusted, 30s for untrusted
      enableResourceMonitoring: true,
    };

    this.sandboxConfigs.set(extensionId, config);
    
    // Create security policy
    const securityPolicy = this.securityService.createDefaultPolicy(extensionId, isTrusted);
    this.securityService.registerExtensionPolicy(securityPolicy);
    
    this.logger.info(`Initialized sandbox for extension: ${extensionId} (trusted: ${isTrusted})`);
    return config;
  }

  /**
   * Execute extension code in sandbox
   */
  async executeInSandbox<T>(
    extensionId: string,
    executionFn: () => Promise<T>,
    timeoutMs?: number
  ): Promise<T> {
    const config = this.sandboxConfigs.get(extensionId);
    if (!config) {
      throw createServiceError(
        'ExtensionSandboxManager',
        'executeInSandbox',
        `No sandbox configuration found for extension: ${extensionId}`,
        { severity: ErrorSeverity.HIGH }
      );
    }

    const context = this.createExecutionContext(extensionId);
    this.executionContexts.set(extensionId, context);

    try {
      // Set up timeout
      const timeout = timeoutMs || config.maxExecutionTime;
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Extension ${extensionId} execution timeout (${timeout}ms)`));
        }, timeout);
      });

      // Execute with timeout
      const result = await Promise.race([
        executionFn(),
        timeoutPromise
      ]);

      // Update execution context
      context.isRunning = false;
      this.updateResourceUsage(extensionId, context);

      return result;

    } catch (error) {
      context.isRunning = false;
      context.violations++;
      
      this.logger.error(`Extension ${extensionId} execution failed:`, error);
      throw error;

    } finally {
      this.executionContexts.delete(extensionId);
    }
  }

  /**
   * Check if extension can require a module
   */
  canRequireModule(extensionId: string, moduleName: string): boolean {
    const config = this.sandboxConfigs.get(extensionId);
    if (!config) {
      return false;
    }

    // Check denied modules first
    if (config.deniedModules.includes(moduleName)) {
      this.logger.warn(`Extension ${extensionId} attempted to require denied module: ${moduleName}`);
      return false;
    }

    // Check allowed modules
    if (config.allowedModules.includes(moduleName)) {
      return true;
    }

    // Check if it's a relative module (extension's own modules)
    if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
      return true;
    }

    this.logger.warn(`Extension ${extensionId} attempted to require unauthorized module: ${moduleName}`);
    return false;
  }

  /**
   * Get sandbox configuration for an extension
   */
  getSandboxConfig(extensionId: string): ExtensionSandboxConfig | undefined {
    return this.sandboxConfigs.get(extensionId);
  }

  /**
   * Update sandbox configuration
   */
  updateSandboxConfig(extensionId: string, updates: Partial<ExtensionSandboxConfig>): void {
    const current = this.sandboxConfigs.get(extensionId);
    if (!current) {
      throw createServiceError(
        'ExtensionSandboxManager',
        'updateSandboxConfig',
        `No sandbox configuration found for extension: ${extensionId}`,
        { severity: ErrorSeverity.MEDIUM }
      );
    }

    const updated = { ...current, ...updates };
    this.sandboxConfigs.set(extensionId, updated);
    
    this.logger.info(`Updated sandbox configuration for extension: ${extensionId}`);
  }

  /**
   * Get execution context for an extension
   */
  getExecutionContext(extensionId: string): ExtensionExecutionContext | undefined {
    return this.executionContexts.get(extensionId);
  }

  /**
   * Get all execution contexts
   */
  getAllExecutionContexts(): ExtensionExecutionContext[] {
    return Array.from(this.executionContexts.values());
  }

  /**
   * Terminate extension execution
   */
  terminateExtension(extensionId: string, reason: string): void {
    const context = this.executionContexts.get(extensionId);
    if (context) {
      context.isRunning = false;
      context.violations++;
      this.executionContexts.delete(extensionId);
    }

    this.logger.warn(`Terminated extension ${extensionId}: ${reason}`);
  }

  /**
   * Check if extension is trusted
   */
  isTrustedExtension(extensionId: string): boolean {
    return TRUSTED_EXTENSIONS.has(extensionId);
  }

  /**
   * Add trusted extension
   */
  addTrustedExtension(extensionId: string): void {
    TRUSTED_EXTENSIONS.add(extensionId);
    this.logger.info(`Added trusted extension: ${extensionId}`);
  }

  /**
   * Remove trusted extension
   */
  removeTrustedExtension(extensionId: string): void {
    TRUSTED_EXTENSIONS.delete(extensionId);
    this.logger.info(`Removed trusted extension: ${extensionId}`);
  }

  /**
   * Get sandbox statistics
   */
  getSandboxStatistics(): {
    totalExtensions: number;
    trustedExtensions: number;
    untrustedExtensions: number;
    runningExtensions: number;
    violationsCount: number;
  } {
    const contexts = Array.from(this.executionContexts.values());
    const configs = Array.from(this.sandboxConfigs.values());
    
    return {
      totalExtensions: configs.length,
      trustedExtensions: configs.filter(c => TRUSTED_EXTENSIONS.has(c.extensionId)).length,
      untrustedExtensions: configs.filter(c => !TRUSTED_EXTENSIONS.has(c.extensionId)).length,
      runningExtensions: contexts.filter(c => c.isRunning).length,
      violationsCount: contexts.reduce((sum, c) => sum + c.violations, 0),
    };
  }

  /**
   * Create execution context for an extension
   */
  private createExecutionContext(extensionId: string): ExtensionExecutionContext {
    return {
      extensionId,
      startTime: Date.now(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      isRunning: true,
      violations: 0,
    };
  }

  /**
   * Update resource usage tracking
   */
  private updateResourceUsage(extensionId: string, context: ExtensionExecutionContext): void {
    const currentMemory = process.memoryUsage();
    const currentCpu = process.cpuUsage(context.cpuUsage);
    
    const memoryDelta = currentMemory.heapUsed - context.memoryUsage.heapUsed;
    const executionTime = Date.now() - context.startTime;
    
    this.securityService.updateResourceUsage(extensionId, {
      memoryUsageMB: memoryDelta / (1024 * 1024),
      cpuUsagePercent: (currentCpu.user + currentCpu.system) / (executionTime * 1000) * 100,
    });
  }

  /**
   * Setup module interceptor for require() calls
   */
  private setupModuleInterceptor(): void {
    // In a real implementation, this would intercept require() calls
    // For now, we'll just set up the framework
    
    this.moduleInterceptor.set('fs', this.createRestrictedFsModule());
    this.moduleInterceptor.set('http', this.createRestrictedHttpModule());
    this.moduleInterceptor.set('https', this.createRestrictedHttpsModule());
    
    this.logger.debug('Module interceptor setup completed');
  }

  /**
   * Create restricted fs module
   */
  private createRestrictedFsModule(): any {
    return {
      readFile: (path: string, ...args: any[]) => {
        // Check file access permissions
        const extensionId = this.getCurrentExtensionId();
        if (extensionId && !this.securityService.canAccessFile(extensionId, path)) {
          throw new Error(`Access denied to file: ${path}`);
        }
        return fs.readFile(path, ...args);
      },
      writeFile: (path: string, ...args: any[]) => {
        const extensionId = this.getCurrentExtensionId();
        if (extensionId && !this.securityService.canAccessFile(extensionId, path)) {
          throw new Error(`Access denied to file: ${path}`);
        }
        return fs.writeFile(path, ...args);
      },
      // Add other fs methods as needed
    };
  }

  /**
   * Create restricted http module
   */
  private createRestrictedHttpModule(): any {
    return {
      request: (options: any, ...args: any[]) => {
        const extensionId = this.getCurrentExtensionId();
        if (extensionId && !this.securityService.hasPermission(extensionId, ExtensionPermission.NETWORK_REQUEST)) {
          throw new Error(`Network access denied for extension: ${extensionId}`);
        }
        return http.request(options, ...args);
      },
    };
  }

  /**
   * Create restricted https module
   */
  private createRestrictedHttpsModule(): any {
    return {
      request: (options: any, ...args: any[]) => {
        const extensionId = this.getCurrentExtensionId();
        if (extensionId && !this.securityService.hasPermission(extensionId, ExtensionPermission.NETWORK_REQUEST)) {
          throw new Error(`Network access denied for extension: ${extensionId}`);
        }
        return https.request(options, ...args);
      },
    };
  }

  /**
   * Get current extension ID (would be set during execution)
   */
  private getCurrentExtensionId(): string | null {
    // In a real implementation, this would track the currently executing extension
    // For now, return null
    return null;
  }

  /**
   * Dispose the sandbox manager
   */
  dispose(): void {
    // Terminate all running extensions
    for (const [extensionId, context] of this.executionContexts) {
      if (context.isRunning) {
        this.terminateExtension(extensionId, 'Sandbox manager disposal');
      }
    }

    this.sandboxConfigs.clear();
    this.executionContexts.clear();
    this.moduleInterceptor.clear();
    
    this.logger.info('Extension sandbox manager disposed');
  }
}
