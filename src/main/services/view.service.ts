import {LoggingServiceAPI} from './logging.service';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {IpcServiceAPI} from './ipc.service';
import {ViewContainersGetRegisteredResult, ViewsGetRegisteredResult} from '@shared/types/ipc';
import {BaseService, IBaseService} from "@services/base.service";
import {BrowserWindow} from "electron";
import {Disposable} from "@shared/types/common";

// Описание View для регистрации
export interface ViewDescriptor {
  id: string; // Полный ID вида, например 'ai-books.characters.list'
  name: string; // Отображаемое имя, например 'Characters'
  componentName: string; // Имя файла компонента без расширения, например 'CharacterListView'
  extensionId: string; // ID расширения, которому принадлежит вид, например 'ai-books.characters'
  location: 'sidebar' | 'panel' | 'statusbar'; // Добавлено 'statusbar'
  icon?: string; // Имя иконки (например, из библиотеки иконок)
  when?: string; // Контекст видимости
  alignment?: 'left' | 'right'; // Выравнивание в статус-баре (для location: 'statusbar')
  priority?: number; // Приоритет для сортировки (для location: 'statusbar', меньше = левее/правее)
}

// Описание View Container (для Activity Bar)
export interface ViewContainerDescriptor {
  id: string; // ID контейнера (часто совпадает с ID расширения)
  title: string; // Всплывающая подсказка
  icon: string; // Имя иконки
  viewId: string; // ID основного вида, который открывается при клике на иконку
}

// API, предоставляемое ViewService для расширений
export interface ViewServiceAPI extends IBaseService {
  registerView(view: ViewDescriptor): Disposable;
  registerViewContainer(container: ViewContainerDescriptor): Disposable;
  getRegisteredViews(): ViewDescriptor[];
  getRegisteredViewContainers(): ViewContainerDescriptor[];
}

// Класс ViewService
export class ViewService extends BaseService implements ViewServiceAPI {
  private views = new Map<string, ViewDescriptor>();
  private viewContainers = new Map<string, ViewContainerDescriptor>();
  private viewDisposables = new Map<string, Disposable>();
  private containerDisposables = new Map<string, Disposable>();
  private readonly ipcService: IpcServiceAPI;
  
  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, "ViewService");
    this.ipcService = ipcService;
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)) {
    await super.initialize(windowGetter);
    this.registerDefaultViews();
    this.registerIpcHandlers();
    this.isInitialized = true;
  }
  
  /**
   * Регистрирует представление (View) в системе.
   * @param view Дескриптор представления для регистрации.
   * @returns Disposable для отмены регистрации.
   */
  registerView(view: ViewDescriptor): Disposable {
    if (!view || !view.id) {
      this.logger.error('Попытка зарегистрировать представление без ID.');
      return {
        dispose: async () => {
          // Пустая операция
        }
      };
    }
    if (this.views.has(view.id)) {
      this.logger.warn(`Представление с ID '${view.id}' уже зарегистрировано. Перезаписываем.`);
      this.viewDisposables.get(view.id)?.dispose();
    }
    if (!view.extensionId) {
      this.logger.error(`Представление с ID '${view.id}' не имеет extensionId.`);
      throw new Error(`Представление '${view.id}' должно иметь extensionId.`);
    }
    if (!view.componentName) {
      this.logger.error(`Представление с ID '${view.id}' не имеет componentName.`);
      throw new Error(`Представление '${view.id}' должно иметь componentName.`);
    }
    this.logger.info(`Регистрация представления: '${view.id}' (Имя: ${view.name}, Расположение: ${view.location}) из расширения '${view.extensionId}'`);
    
    this.views.set(view.id, view);
    
    const disposable = {
      dispose: async () => {
        this.logger.info(`Отмена регистрации представления: '${view.id}'`);
        this.views.delete(view.id);
        this.viewDisposables.delete(view.id);
      }
    };
    this.viewDisposables.set(view.id, disposable);
    return disposable;
  }
  
  /**
   * Регистрирует контейнер представлений в системе.
   * @param container Дескриптор контейнера для регистрации.
   * @returns Disposable для отмены регистрации.
   */
  registerViewContainer(container: ViewContainerDescriptor): Disposable {
    if (!container || !container.id) {
      this.logger.error('Попытка зарегистрировать контейнер представлений без ID.');
      return {
        dispose: async () => {
          // Пустая операция
        }
      };
    }
    if (this.viewContainers.has(container.id)) {
      this.logger.warn(`Контейнер представлений с ID '${container.id}' уже зарегистрирован. Перезаписываем.`);
    }
    this.logger.info(`Регистрация контейнера представлений: '${container.id}' (${container.title})`);
    this.containerDisposables.get(container.id)?.dispose();
    
    this.viewContainers.set(container.id, container);
    
    const disposable = {
      dispose: async () => {
        this.logger.info(`Удаление регистрации контейнера представлений '${container.id}'`);
        this.viewContainers.delete(container.id);
        this.containerDisposables.delete(container.id);
      }
    };
    this.containerDisposables.set(container.id, disposable);
    return disposable;
  }
  
  /**
   * Возвращает все зарегистрированные представления.
   * @returns Массив дескрипторов представлений.
   */
  getRegisteredViews(): ViewDescriptor[] {
    return Array.from(this.views.values());
  }
  
  /**
   * Возвращает все зарегистрированные контейнеры представлений.
   * @returns Массив дескрипторов контейнеров.
   */
  getRegisteredViewContainers(): ViewContainerDescriptor[] {
    return Array.from(this.viewContainers.values());
  }
  
  async dispose() {
    this.logger.info('Удаление всех зарегистрированных представлений и контейнеров...');
    this.viewDisposables.forEach(d => d.dispose());
    this.containerDisposables.forEach(d => d.dispose());
    this.views.clear();
    this.viewContainers.clear();
    this.viewDisposables.clear();
    this.containerDisposables.clear();
    await super.dispose();
  }
  
  /**
   * Регистрирует представления по умолчанию.
   */
  private registerDefaultViews() {
    // ViewService обрабатывает перезаписи, проверка не нужна
    this.registerView({
      id: 'workbench.views.notifications',
      name: 'Уведомления',
      componentName: 'NotificationsView',
      extensionId: 'workbench',
      location: 'panel',
      icon: 'Bell',
    });
    
    // Регистрация представления результатов AI для панели
    this.registerView({
      id: 'workbench.views.aiResults',
      name: 'AI Анализ',
      componentName: 'AIResultsView',
      extensionId: 'workbench',
      location: 'panel',
      icon: 'Sparkle',
    });
  }
  
  /**
   * Регистрирует обработчики IPC для ViewService.
   */
  private registerIpcHandlers(): void {
    this.logger.info('Регистрация обработчиков IPC для ViewService...');
    
    this.ipcService.handle<never, ViewsGetRegisteredResult>(
      IpcChannels.VIEWS_GET_REGISTERED,
      () => {
        return this.getRegisteredViews();
      }
    );
    
    this.ipcService.handle<never, ViewContainersGetRegisteredResult>(
      IpcChannels.VIEWS_GET_REGISTERED_CONTAINERS,
      () => {
        return this.getRegisteredViewContainers();
      }
    );
    
    this.logger.info('Обработчики IPC для ViewService зарегистрированы.');
  }
}
