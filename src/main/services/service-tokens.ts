/**
 * Service tokens for type-safe dependency injection
 * These tokens provide compile-time type safety and runtime service identification
 *
 * Note: This file uses 'any' types for generic service token patterns.
 */
/* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-extraneous-class */

import { LoggingServiceAPI } from './logging.service';
import { StorageServiceAPI } from './storage.service';
import { IpcServiceAPI } from './ipc.service';
import { CommandServiceAPI } from './command.service';
import { ViewServiceAPI } from './view.service';
import { ConfigurationServiceAPI } from './configuration.service';
import { NotificationServiceAPI } from './notification.service';
import { DialogServiceAPI } from './dialog.service';
import { ContextServiceAPI } from './context.service';
import { MenuServiceAPI } from './menu.service';
import { EditorServiceAPI } from './editor.service';
import { GitServiceAPI } from './git.service';
import { AIServiceAPI } from './ai.service';
import { KeybindingServiceAPI } from './keybinding.service';
import { AppLifecycle } from '@shared/types/lifecycle-events';
import { HealthService } from './health.service';
import { LoggingService } from './logging.service';
import { StorageService } from './storage.service';
import { IpcService } from './ipc.service';
import { ConfigurationService } from './configuration.service';
import { CommandService } from './command.service';
import { ViewService } from './view.service';
import { ContextService } from './context.service';
import { KeybindingService } from './keybinding.service';
import { NotificationService } from './notification.service';
import { DialogService } from './dialog.service';
import { MenuService } from './menu.service';
import { EditorService } from './editor.service';
import { GitService } from './git.service';
import { AIService } from './ai.service';
import { LifecycleService } from './lifecycle.service';

/**
 * Service token type for type-safe service identification
 */
export interface ServiceToken<T> {
  readonly name: string;
  readonly description?: string;
  readonly _type?: T; // Phantom type for TypeScript inference
}

/**
 * Create a service token
 */
export function createServiceToken<T>(name: string, description?: string): ServiceToken<T> {
  return {
    name,
    description,
  };
}

/**
 * Core service tokens
 */
export const ServiceTokens = {
  // Core infrastructure services
  Logging: createServiceToken<LoggingServiceAPI>('logging', 'Application logging service'),
  Storage: createServiceToken<StorageServiceAPI>('storage', 'Database storage service'),
  Ipc: createServiceToken<IpcServiceAPI>('ipc', 'Inter-process communication service'),
  Configuration: createServiceToken<ConfigurationServiceAPI>('configuration', 'Application configuration service'),
  Health: createServiceToken<HealthService>('health', 'Service health monitoring'),
  
  // Application services
  Commands: createServiceToken<CommandServiceAPI>('commands', 'Command execution service'),
  Views: createServiceToken<ViewServiceAPI>('views', 'View management service'),
  Notifications: createServiceToken<NotificationServiceAPI>('notifications', 'User notification service'),
  Dialogs: createServiceToken<DialogServiceAPI>('dialogs', 'Dialog management service'),
  Context: createServiceToken<ContextServiceAPI>('context', 'Application context service'),
  Menus: createServiceToken<MenuServiceAPI>('menus', 'Menu management service'),
  Editors: createServiceToken<EditorServiceAPI>('editors', 'Editor management service'),
  Keybinding: createServiceToken<KeybindingServiceAPI>('keybinding', 'Keyboard shortcut service'),
  
  // External services
  Git: createServiceToken<GitServiceAPI>('git', 'Git version control service'),
  AI: createServiceToken<AIServiceAPI>('ai', 'AI integration service'),
  
  // Lifecycle
  Lifecycle: createServiceToken<AppLifecycle>('lifecycle', 'Application lifecycle management'),
} as const;

/**
 * Service token registry for runtime lookup
 */
export class ServiceTokenRegistry {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private static readonly tokens = new Map<string, ServiceToken<any>>();

  private constructor() {
    // Private constructor to prevent instantiation
  }
  
  /**
   * Register a service token
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static register<T>(token: ServiceToken<T>): void {
    this.tokens.set(token.name, token);
  }
  
  /**
   * Get a service token by name
   */
  static get<T>(name: string): ServiceToken<T> | undefined {
    return this.tokens.get(name);
  }
  
  /**
   * Get all registered tokens
   */
  static getAll(): ServiceToken<any>[] {
    return Array.from(this.tokens.values());
  }
  
  /**
   * Check if a token is registered
   */
  static has(name: string): boolean {
    return this.tokens.has(name);
  }
}

// Register all core service tokens
Object.values(ServiceTokens).forEach((token: ServiceToken<any>) => {
  ServiceTokenRegistry.register(token);
});

/**
 * Dependency injection decorators for automatic dependency resolution
 */

/**
 * Injectable decorator for marking classes as injectable services
 */
export function Injectable<T extends new (...args: any[]) => any>(
  token?: ServiceToken<InstanceType<T>>,
  dependencies?: ServiceToken<any>[]
) {
  return function(constructor: T): T {
    // Store metadata for dependency injection
    Reflect.defineMetadata('di:injectable', true, constructor);
    if (token) {
      Reflect.defineMetadata('di:token', token, constructor);
    }
    if (dependencies) {
      Reflect.defineMetadata('di:dependencies', dependencies, constructor);
    }
    return constructor;
  };
}

/**
 * Inject decorator for marking constructor parameters for injection
 */
export function Inject<T>(token: ServiceToken<T>) {
  return function(target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
    const existingTokens = Reflect.getMetadata('di:inject', target) || [];
    existingTokens[parameterIndex] = token;
    Reflect.defineMetadata('di:inject', existingTokens, target);
  };
}

/**
 * Optional decorator for marking optional dependencies
 */
export function Optional(target: any, propertyKey: string | symbol | undefined, parameterIndex: number) {
  const existingOptional = Reflect.getMetadata('di:optional', target) || [];
  existingOptional[parameterIndex] = true;
  Reflect.defineMetadata('di:optional', existingOptional, target);
}

/**
 * Utility functions for working with service tokens
 */
export class ServiceTokenUtils {
  private constructor() {
    // Private constructor to prevent instantiation
  }
  /**
   * Get dependency tokens from a constructor
   */
  static getDependencyTokens(constructor: any): ServiceToken<any>[] {
    return Reflect.getMetadata('di:dependencies', constructor) || 
           Reflect.getMetadata('di:inject', constructor) || [];
  }
  
  /**
   * Get service token from a constructor
   */
  static getServiceToken(constructor: any): ServiceToken<any> | undefined {
    return Reflect.getMetadata('di:token', constructor);
  }
  
  /**
   * Check if a constructor is injectable
   */
  static isInjectable(constructor: any): boolean {
    return Reflect.getMetadata('di:injectable', constructor) === true;
  }
  
  /**
   * Get optional parameter indices
   */
  static getOptionalParameters(constructor: any): number[] {
    const optional = Reflect.getMetadata('di:optional', constructor) || [];
    return optional.map((isOptional: boolean, index: number) => isOptional ? index : -1)
                  .filter((index: number) => index !== -1);
  }
}

/**
 * Type-safe service resolver interface
 */
export interface ServiceResolver {
  resolve<T>(token: ServiceToken<T>): Promise<T>;
  resolveOptional<T>(token: ServiceToken<T>): Promise<T | undefined>;
  isRegistered<T>(token: ServiceToken<T>): boolean;
}

/**
 * Service factory interface for creating services with dependencies
 */
export interface ServiceFactory<T> {
  create(resolver: ServiceResolver): Promise<T>;
}

/**
 * Service configuration interface
 */
export interface ServiceConfiguration {
  token: ServiceToken<any>;
  factory?: ServiceFactory<any>;
  constructor?: new (...args: any[]) => any;
  dependencies?: ServiceToken<any>[];
  lifecycle?: 'singleton' | 'transient' | 'scoped';
  lazy?: boolean;
  tags?: string[];
}

/**
 * Service module interface for organizing related services
 */
export interface ServiceModule {
  name: string;
  services: ServiceConfiguration[];
  imports?: ServiceModule[];
  exports?: ServiceToken<any>[];
}

/**
 * Core services module
 */
export const CoreServicesModule: ServiceModule = {
  name: 'CoreServices',
  services: [
    {
      token: ServiceTokens.Logging,
      constructor: LoggingService,
      lifecycle: 'singleton',
      tags: ['core', 'infrastructure'],
    },
    {
      token: ServiceTokens.Storage,
      constructor: StorageService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['core', 'infrastructure'],
    },
    {
      token: ServiceTokens.Ipc,
      constructor: IpcService,
      dependencies: [ServiceTokens.Logging],
      lifecycle: 'singleton',
      tags: ['core', 'infrastructure'],
    },
    {
      token: ServiceTokens.Configuration,
      constructor: ConfigurationService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['core'],
    },
    {
      token: ServiceTokens.Health,
      constructor: HealthService,
      dependencies: [ServiceTokens.Logging],
      lifecycle: 'singleton',
      tags: ['core', 'monitoring'],
    },
    {
      token: ServiceTokens.Commands,
      constructor: CommandService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Context, ServiceTokens.Keybinding, ServiceTokens.Ipc, ServiceTokens.Configuration],
      lifecycle: 'singleton',
      tags: ['core'],
    },
    {
      token: ServiceTokens.Views,
      constructor: ViewService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['core'],
    },
    {
      token: ServiceTokens.Context,
      constructor: ContextService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['core'],
    },
    {
      token: ServiceTokens.Keybinding,
      constructor: KeybindingService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Context, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['core'],
    },
    {
      token: ServiceTokens.Notifications,
      constructor: NotificationService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['ui'],
    },
    {
      token: ServiceTokens.Dialogs,
      constructor: DialogService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['ui'],
    },
    {
      token: ServiceTokens.Menus,
      constructor: MenuService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Commands, ServiceTokens.Context, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['ui'],
    },
    {
      token: ServiceTokens.Editors,
      constructor: EditorService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc],
      lifecycle: 'singleton',
      tags: ['core'],
    },
    {
      token: ServiceTokens.Git,
      constructor: GitService,
      dependencies: [ServiceTokens.Logging],
      lifecycle: 'singleton',
      tags: ['external'],
    },
    {
      token: ServiceTokens.AI,
      constructor: AIService,
      dependencies: [ServiceTokens.Logging, ServiceTokens.Ipc, ServiceTokens.Configuration],
      lifecycle: 'singleton',
      tags: ['external'],
    },
    {
      token: ServiceTokens.Lifecycle,
      constructor: LifecycleService,
      dependencies: [ServiceTokens.Logging],
      lifecycle: 'singleton',
      tags: ['core', 'infrastructure'],
    },
  ],
  exports: Object.values(ServiceTokens),
};
