/**
 * Service container builder for configuring and building the dependency injection container
 *
 * Note: This file uses 'any' types for generic service registration patterns.
 */
/* eslint-disable @typescript-eslint/no-explicit-any */

import 'reflect-metadata';
import { DependencyInjectionContainer, ServiceRegistrationOptions, ServiceIdentifier } from './dependency-injection.service';
import { ServiceToken, ServiceModule, ServiceConfiguration, ServiceResolver } from './service-tokens';
import { LoggingServiceAPI } from './logging.service';
import { createServiceError, ErrorSeverity } from '@shared/errors';

// Type compatibility helper
function tokenToIdentifier<T>(token: ServiceToken<T>): ServiceIdentifier<T> {
  return token.name as ServiceIdentifier<T>;
}

// Helper to convert ServiceToken dependencies to ServiceIdentifier dependencies
function convertDependencies(dependencies: ServiceToken<any>[]): ServiceIdentifier<any>[] {
  return dependencies.map(dep => tokenToIdentifier(dep));
}

// Extended options for the builder that accepts ServiceToken dependencies
interface BuilderServiceRegistrationOptions extends Omit<ServiceRegistrationOptions, 'dependencies'> {
  dependencies?: ServiceToken<any>[];
}

/**
 * Service container builder for fluent configuration
 */
export class ServiceContainerBuilder {
  private readonly container: DependencyInjectionContainer;
  private readonly logger: LoggingServiceAPI;
  private readonly modules: ServiceModule[] = [];

  constructor(logger: LoggingServiceAPI) {
    this.logger = logger.createScopedLogger('ServiceContainerBuilder');
    this.container = new DependencyInjectionContainer(logger);
  }

  /**
   * Register a service with the container
   */
  register<T>(
    token: ServiceToken<T>,
    factoryOrConstructor: ((resolver: ServiceResolver) => T | Promise<T>) | (new (...args: any[]) => T),
    options: BuilderServiceRegistrationOptions = {}
  ): this {
    // Convert dependencies from tokens to identifiers
    const dependencies = options.dependencies ? convertDependencies(options.dependencies) : [];

    this.container.register(
      tokenToIdentifier(token),
      factoryOrConstructor as any,
      dependencies,
      { ...options, dependencies }
    );

    this.logger.debug(`Registered service: ${token.name}`);
    return this;
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    token: ServiceToken<T>,
    factoryOrConstructor: ((resolver: ServiceResolver) => T | Promise<T>) | (new (...args: any[]) => T),
    dependencies: ServiceToken<any>[] = []
  ): this {
    return this.register(token, factoryOrConstructor, {
      lifecycle: 'singleton',
      dependencies,
    });
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    token: ServiceToken<T>,
    factoryOrConstructor: ((resolver: ServiceResolver) => T | Promise<T>) | (new (...args: any[]) => T),
    dependencies: ServiceToken<any>[] = []
  ): this {
    return this.register(token, factoryOrConstructor, {
      lifecycle: 'transient',
      dependencies,
    });
  }

  /**
   * Register an existing instance
   */
  registerInstance<T>(token: ServiceToken<T>, instance: T): this {
    this.container.registerInstance(tokenToIdentifier(token), instance);
    this.logger.debug(`Registered instance: ${token.name}`);
    return this;
  }

  /**
   * Register services from a module
   */
  registerModule(module: ServiceModule): this {
    this.modules.push(module);
    this.logger.debug(`Registered module: ${module.name}`);

    // Register imported modules first
    if (module.imports) {
      for (const importedModule of module.imports) {
        this.registerModule(importedModule);
      }
    }

    // Register services from the module
    for (const serviceConfig of module.services) {
      this.registerServiceConfiguration(serviceConfig);
    }

    return this;
  }

  /**
   * Register a service from configuration
   */
  private registerServiceConfiguration(config: ServiceConfiguration): void {
    const options: ServiceRegistrationOptions = {
      lifecycle: config.lifecycle || 'singleton',
      lazy: config.lazy || false,
      tags: config.tags || [],
      dependencies: (config.dependencies || []).map(dep => tokenToIdentifier(dep)),
    };

    if (config.factory) {
      // Use factory
      this.container.register(
        tokenToIdentifier(config.token),
        (container) => {
          if (!config.factory) {
            throw new Error('Factory is required but not provided');
          }
          return config.factory.create(this.createResolver(container));
        },
        (config.dependencies || []).map(dep => tokenToIdentifier(dep)),
        options
      );
    } else if (config.constructor) {
      // Use constructor
      this.container.register(
        tokenToIdentifier(config.token),
        config.constructor,
        (config.dependencies || []).map(dep => tokenToIdentifier(dep)),
        options
      );
    } else {
      throw createServiceError(
        'ServiceContainerBuilder',
        'registerServiceConfiguration',
        `Service configuration must have either factory or constructor: ${config.token.name}`,
        { severity: ErrorSeverity.HIGH }
      );
    }
  }

  /**
   * Create a service resolver for factories
   */
  private createResolver(container: DependencyInjectionContainer): ServiceResolver {
    return {
      async resolve<T>(token: ServiceToken<T>): Promise<T> {
        return container.resolve(tokenToIdentifier(token));
      },

      async resolveOptional<T>(token: ServiceToken<T>): Promise<T | undefined> {
        try {
          return await container.resolve(tokenToIdentifier(token));
        } catch {
          return undefined;
        }
      },

      isRegistered<T>(token: ServiceToken<T>): boolean {
        return container.isRegistered(tokenToIdentifier(token));
      },
    };
  }

  /**
   * Auto-register services using reflection metadata
   */
  autoRegister(constructors: (new (...args: any[]) => any)[]): this {
    for (const constructor of constructors) {
      const isInjectable = Reflect.getMetadata('di:injectable', constructor);
      if (!isInjectable) {
        this.logger.warn(`Constructor ${constructor.name} is not marked as @Injectable, skipping auto-registration`);
        continue;
      }

      const token = Reflect.getMetadata('di:token', constructor);
      if (!token) {
        this.logger.warn(`Constructor ${constructor.name} has no service token, skipping auto-registration`);
        continue;
      }

      const dependencies = Reflect.getMetadata('di:dependencies', constructor) || [];
      
      this.registerSingleton(token, constructor, dependencies);
      this.logger.debug(`Auto-registered service: ${token.name}`);
    }

    return this;
  }

  /**
   * Configure container settings
   */
  configure(configureFn: (container: DependencyInjectionContainer) => void): this {
    configureFn(this.container);
    return this;
  }

  /**
   * Validate service registrations
   */
  validate(): this {
    const registrations = this.container.getAllRegistrations();
    const errors: string[] = [];

    for (const registration of registrations) {
      // Check for circular dependencies
      const visited = new Set<any>();
      const visiting = new Set<any>();
      
      if (this.hasCircularDependency(registration.identifier, visited, visiting)) {
        errors.push(`Circular dependency detected for service: ${String(registration.identifier)}`);
      }

      // Check if all dependencies are registered
      for (const dependency of registration.dependencies) {
        if (!this.container.isRegistered(dependency)) {
          const depName = this.getServiceName(dependency);
          const serviceName = this.getServiceName(registration.identifier);
          errors.push(`Unregistered dependency '${depName}' for service '${serviceName}'`);
        }
      }
    }

    if (errors.length > 0) {
      throw createServiceError(
        'ServiceContainerBuilder',
        'validate',
        `Service registration validation failed:\n${errors.join('\n')}`,
        { severity: ErrorSeverity.HIGH }
      );
    }

    this.logger.info('Service registration validation passed');
    return this;
  }

  /**
   * Get service name for logging
   */
  private getServiceName(identifier: any): string {
    if (typeof identifier === 'string') {
      return identifier;
    }
    if (typeof identifier === 'symbol') {
      return identifier.toString();
    }
    if (typeof identifier === 'function') {
      return identifier.name || 'AnonymousService';
    }
    if (identifier && typeof identifier === 'object' && identifier.name) {
      return identifier.name;
    }
    return 'UnknownService';
  }

  /**
   * Check for circular dependencies
   */
  private hasCircularDependency(
    serviceId: any,
    visited: Set<any>,
    visiting: Set<any>
  ): boolean {
    if (visiting.has(serviceId)) {
      return true; // Circular dependency found
    }

    if (visited.has(serviceId)) {
      return false; // Already processed
    }

    visiting.add(serviceId);

    const registration = this.container.getRegistration(serviceId);
    if (registration) {
      for (const dependency of registration.dependencies) {
        if (this.hasCircularDependency(dependency, visited, visiting)) {
          return true;
        }
      }
    }

    visiting.delete(serviceId);
    visited.add(serviceId);
    return false;
  }

  /**
   * Build and return the configured container
   */
  build(): DependencyInjectionContainer {
    this.validate();
    this.logger.info('Service container built successfully');
    return this.container;
  }

  /**
   * Get container statistics
   */
  getStatistics(): {
    totalServices: number;
    singletonServices: number;
    transientServices: number;
    scopedServices: number;
    modulesRegistered: number;
    servicesByTag: Record<string, number>;
  } {
    const registrations = this.container.getAllRegistrations();
    const stats = {
      totalServices: registrations.length,
      singletonServices: 0,
      transientServices: 0,
      scopedServices: 0,
      modulesRegistered: this.modules.length,
      servicesByTag: {} as Record<string, number>,
    };

    for (const registration of registrations) {
      switch (registration.options.lifecycle) {
        case 'singleton':
          stats.singletonServices++;
          break;
        case 'transient':
          stats.transientServices++;
          break;
        case 'scoped':
          stats.scopedServices++;
          break;
      }

      // Count services by tag
      for (const tag of registration.options.tags || []) {
        stats.servicesByTag[tag] = (stats.servicesByTag[tag] || 0) + 1;
      }
    }

    return stats;
  }

  /**
   * Create a child container with inherited registrations
   */
  createChild(): ServiceContainerBuilder {
    const childBuilder = new ServiceContainerBuilder(this.logger);
    
    // Copy registrations from parent
    const registrations = this.container.getAllRegistrations();
    for (const registration of registrations) {
      if (registration.factory) {
        childBuilder.container.register(
          registration.identifier,
          registration.factory,
          registration.dependencies,
          registration.options
        );
      } else if (registration.constructor) {
        childBuilder.container.register(
          registration.identifier,
          registration.constructor,
          registration.dependencies,
          registration.options
        );
      }
    }

    this.logger.debug('Created child container');
    return childBuilder;
  }

  /**
   * Export container configuration for debugging
   */
  exportConfiguration(): {
    modules: string[];
    services: {
      token: string;
      lifecycle: string;
      dependencies: string[];
      tags: string[];
    }[];
  } {
    const registrations = this.container.getAllRegistrations();
    
    return {
      modules: this.modules.map(m => m.name),
      services: registrations.map(reg => ({
        token: reg.identifier.toString(),
        lifecycle: reg.options.lifecycle || 'singleton',
        dependencies: reg.dependencies.map(dep => dep.toString()),
        tags: reg.options.tags || [],
      })),
    };
  }
}
